﻿using Witlab.Platform.Core.WitLab;
using Witlab.Platform.Core.WitLab.Interfaces;

namespace Witlab.Platform.UseCases.WitLab.CreateOrUpdateLabAccessKeyParis;
public class CreateOrUpdateLabAccessKeysHandler : ICommandHandler<CreateOrUpdateLabAccessKeysCommand, Result<LabAccessKeysDTO>>
{
  private readonly ILabAccessKeysService _labAccessKeysService; 

  public CreateOrUpdateLabAccessKeysHandler(ILabAccessKeysService labAccessKeysService)
  {
    _labAccessKeysService = labAccessKeysService ?? throw new ArgumentNullException(nameof(labAccessKeysService));
  }

  public async Task<Result<LabAccessKeysDTO>> Handle(CreateOrUpdateLabAccessKeysCommand request, CancellationToken cancellationToken)
  {
    var existingKeysResult = await _labAccessKeysService.GetKeysByUserNameAndDeptCode(request.userName, request.deptCode, cancellationToken);
    if (existingKeysResult.IsSuccess)
    {
      var existingKeys = existingKeysResult.Value;
      if (existingKeys.AccessKey != request.accessKey && existingKeys.SecretKey != request.secretKey)
      {
        await _labAccessKeysService.DeleteKeysAsync(existingKeys.Id, cancellationToken);
      }
    }

    var createResult = await _labAccessKeysService.CreateKeysAsync(request.userName, request.deptCode, request.accessKey, request.secretKey, cancellationToken);
    if (createResult.IsError())
    {
      return Result.Error(new ErrorList(createResult.Errors));
    }

    var newKeys = createResult.Value;

    return Result.Success(new LabAccessKeysDTO(newKeys.UserName,
                                              newKeys.DeptCode,
                                              newKeys.RoleCode,
                                              newKeys.AccessKey,
                                              newKeys.SecretKey,
                                              newKeys.State.Value,
                                              newKeys.State.Name));
  }
}
