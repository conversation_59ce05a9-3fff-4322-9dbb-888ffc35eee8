﻿namespace Witlab.Platform.Infrastructure.Auth.Models;

/// <summary>
/// 用户信息
/// </summary>
public class UserInfo
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string UserName { get; set; } = string.Empty;

    /// <summary>
    /// 姓名
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 电话
    /// </summary>
    public long? Phone { get; set; }

    /// <summary>
    /// 头像
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 角色列表
    /// </summary>
    public List<string> Roles { get; set; } = [];

    /// <summary>
    /// 权限列表
    /// </summary>
    public List<string> Permissions { get; set; } = [];
}
