﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace Witlab.Platform.Infrastructure.WitLab.Extensions;
public static class WitLabApiRequestConverter
{
  public static async Task<WitLabApiRequest> ToWitLabApiRequest(this HttpRequest httpRequest, CancellationToken cancellationToken = default)
  {
    var request = new WitLabApiRequest
    {
      Method = httpRequest.Method.ToString(),
      Headers = httpRequest.Headers.ToDictionary(h => h.Key, h => h.Value.ToList()),
      ContentType = httpRequest.ContentType
    };
    if (httpRequest.Method == HttpMethod.Post.ToString())
    {
      // 复制 httpRequest.Body 到 request.Body
      var memoryStream = new MemoryStream();
      if (httpRequest.Body.CanSeek)
      {
        httpRequest.Body.Position = 0;
      }
      await httpRequest.Body.CopyToAsync(memoryStream, cancellationToken);
      memoryStream.Position = 0;
      request.Body = memoryStream;
    }
    else if (!string.IsNullOrEmpty(httpRequest.ContentType))
    {
      request.Body = new MemoryStream();
    }
    return request;
  }
}
