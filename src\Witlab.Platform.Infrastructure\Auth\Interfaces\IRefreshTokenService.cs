﻿using Ardalis.Result;
using Witlab.Platform.Infrastructure.Auth.Models;

namespace Witlab.Platform.Infrastructure.Auth.Interfaces;

/// <summary>
/// 刷新令牌服务接口
/// </summary>
public interface IRefreshTokenService
{
    /// <summary>
    /// 创建刷新令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="refreshToken">刷新令牌</param>
    /// <param name="expiresAt">过期时间</param>
    /// <returns>创建的刷新令牌</returns>
    Task<Result<RefreshToken>> CreateRefreshTokenAsync(Guid userId, string refreshToken, DateTime expiresAt);

    /// <summary>
    /// 获取刷新令牌
    /// </summary>
    /// <param name="token">令牌</param>
    /// <returns>刷新令牌</returns>
    Task<Result<RefreshToken?>> GetRefreshTokenAsync(string token);

    /// <summary>
    /// 使用刷新令牌
    /// </summary>
    /// <param name="token">令牌</param>
    /// <returns>操作结果</returns>
    Task<Result> UseRefreshTokenAsync(string token);

    /// <summary>
    /// 撤销刷新令牌
    /// </summary>
    /// <param name="token">令牌</param>
    /// <returns>操作结果</returns>
    Task<Result> RevokeRefreshTokenAsync(string token);

    /// <summary>
    /// 撤销用户的所有刷新令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>操作结果</returns>
    Task<Result> RevokeAllUserRefreshTokensAsync(Guid userId);
}
