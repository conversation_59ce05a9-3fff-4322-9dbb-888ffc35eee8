﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Witlab.Platform.Core.WitLab.Interfaces;
public interface ILabAccessKeysService
{
  Task<Result<LabAccessKeys>> GetKeysByUserNameAndDeptCode(string userName, string deptCode, CancellationToken cancellationToken = default);

  Task<Result<LabAccessKeys>> CreateKeysAsync(string userName, string deptCode, string accessKey, string secretKey, CancellationToken cancellationToken = default);

  Task<Result> DeleteKeysAsync(Guid guid, CancellationToken cancellationToken = default);
}
