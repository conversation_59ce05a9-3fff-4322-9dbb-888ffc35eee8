﻿﻿using Witlab.Platform.UseCases.Platform.Depts.Get;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Depts;

/// <summary>
/// 根据ID获取部门
/// </summary>
[RequirePermission("dept:view")]
public class GetById(IMediator _mediator) : Endpoint<GetDeptByIdRequest, DeptRecord>
{
    public override void Configure()
    {
        Get(GetDeptByIdRequest.Route);
        Description(x => x.AutoTagOverride("Dept"));
        Summary(s =>
        {
            s.Summary = "根据ID获取部门";
            s.Description = "根据部门ID获取部门详细信息";
        });
    }

    public override async Task HandleAsync(GetDeptByIdRequest request, CancellationToken cancellationToken)
    {
        var query = new GetDeptQuery(request.DeptId);

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
        {
            var dto = result.Value;
            Response = new DeptRecord()
            {
              DeptId = dto.Id,
              DeptName = dto.DeptName,
              DeptCode = dto.DeptCode,
              ParentId = dto.ParentId,
              Leader = dto.Leader,
              OrderNum = dto.OrderNum,
              Status = dto.StateValue,
              Remark = dto.Remark,
              CreatedOnUtc = dto.CreatedOnUtc,
              CreatedBy = dto.CreatedBy,
              LastModifiedOnUtc = dto.LastModifiedOnUtc,
              LastModifiedBy = dto.LastModifiedBy
            };
            return;
        }

        // 处理错误
        if (result.Status == ResultStatus.NotFound)
        {
            await SendNotFoundAsync(cancellationToken);
            return;
        }

        await SendErrorsAsync(cancellation: cancellationToken);
    }
}
