﻿using MediatR;
using Microsoft.Extensions.Logging;
using Witlab.Platform.Core.Platform.Events;

namespace Witlab.Platform.Infrastructure.Platform.Handlers;

/// <summary>
/// 权限授予事件处理器
/// </summary>
public class PermissionGrantedHandler : INotificationHandler<PermissionGrantedEvent>
{
    private readonly ILogger<PermissionGrantedHandler> _logger;

    public PermissionGrantedHandler(ILogger<PermissionGrantedHandler> logger)
    {
        _logger = logger;
    }

    public Task Handle(PermissionGrantedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("处理权限授予事件: 角色ID {RoleId}, 权限ID {PermissionId}, 权限编码 {PermissionCode}", 
            notification.RoleId, notification.PermissionId, notification.PermissionCode);

        // 这里可以添加权限授予后的业务逻辑
        return Task.CompletedTask;
    }
}
