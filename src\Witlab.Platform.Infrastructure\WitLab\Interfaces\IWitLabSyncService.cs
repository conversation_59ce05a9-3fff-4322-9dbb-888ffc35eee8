﻿using Ardalis.Result;

namespace Witlab.Platform.Infrastructure.WitLab.Interfaces;
public interface IWitLabSyncService
{
  public Task<Result> SyncAddDeptAsync(string deptCode, string shareDB = "External", string? deptIdentificationCode = null, CancellationToken cancellationToken = default);

  public Task<Result> SyncUpdateDeptAsync(string deptCode, string fieldName, object fieldValue, CancellationToken cancellationToken = default);

  public Task<Result> SyncDeleteDeptAsync(string deptCode, CancellationToken cancellationToken = default);

  public Task<Result> SyncAddRoleAsync(string roleCode, string roleName, CancellationToken cancellationToken = default);

  public Task<Result> SyncUpdateRoleAsync(string roleCode, string fieldName, object fieldValue, CancellationToken cancellationToken = default);

  public Task<Result> SyncDeleteRoleAsync(string roleCode, CancellationToken cancellationToken = default);

  public Task<Result> SyncAddUserInfoAsync(string userName, string fullName, string? email, CancellationToken cancellationToken = default);

  public Task<Result> SyncChangeUserPasswordAsync(string userName, string password, CancellationToken cancellationToken = default);

  public Task<Result> SyncUserDeptsAsync(string userName, List<string> deptCodes, CancellationToken cancellationToken = default);

  public Task<Result> SyncUserRolesAsync(string userName, List<string> roleCodes, CancellationToken cancellationToken = default);

  public Task<Result<WitLabApiResponse>> GetUserAccessKeysAsync(string userName, string deptCode, CancellationToken cancellationToken = default);

}
