﻿using WitLab.Platform.SharedKernel;

namespace Witlab.Platform.Infrastructure.Auth.Models;

/// <summary>
/// 刷新令牌实体
/// </summary>
public class RefreshToken
{
  public Guid Id { get; private set; } = Guid.NewGuid();
  /// <summary>
  /// 用户ID
  /// </summary>
  public Guid UserId { get; private set; }

  /// <summary>
  /// 令牌
  /// </summary>
  public string Token { get; private set; } = string.Empty;

  /// <summary>
  /// 过期时间
  /// </summary>
  public DateTime ExpiresAt { get; private set; }

  /// <summary>
  /// 创建时间
  /// </summary>
  public DateTime CreatedAt { get; private set; }

  /// <summary>
  /// 是否已使用
  /// </summary>
  public bool IsUsed { get; private set; }

  /// <summary>
  /// 是否已撤销
  /// </summary>
  public bool IsRevoked { get; private set; }

  /// <summary>
  /// 是否已过期
  /// </summary>
  public bool IsExpired => DateTime.UtcNow >= ExpiresAt;

  /// <summary>
  /// 是否有效
  /// </summary>
  public bool IsActive => !IsUsed && !IsRevoked && !IsExpired;

  private RefreshToken() { }

  public RefreshToken(Guid userId, string token, DateTime expiresAt)
  {
    UserId = userId;
    Token = token;
    ExpiresAt = expiresAt;
    CreatedAt = DateTime.UtcNow;
    IsUsed = false;
    IsRevoked = false;
  }

  /// <summary>
  /// 使用令牌
  /// </summary>
  public void Use()
  {
    IsUsed = true;
  }

  /// <summary>
  /// 撤销令牌
  /// </summary>
  public void Revoke()
  {
    IsRevoked = true;
  }
}
