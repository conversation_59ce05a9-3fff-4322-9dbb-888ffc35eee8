{"name": "@vben-core/shared", "version": "5.5.4", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/@vben-core/base/shared"}, "license": "MIT", "type": "module", "scripts": {"build": "pnpm unbuild", "stub": "pnpm unbuild --stub"}, "files": ["dist"], "sideEffects": false, "exports": {"./constants": {"types": "./src/constants/index.ts", "development": "./src/constants/index.ts", "default": "./dist/constants/index.mjs"}, "./utils": {"types": "./src/utils/index.ts", "development": "./src/utils/index.ts", "default": "./dist/utils/index.mjs"}, "./color": {"types": "./src/color/index.ts", "development": "./src/color/index.ts", "default": "./dist/color/index.mjs"}, "./cache": {"types": "./src/cache/index.ts", "development": "./src/cache/index.ts", "default": "./dist/cache/index.mjs"}, "./store": {"types": "./src/store.ts", "development": "./src/store.ts", "default": "./dist/store.mjs"}, "./global-state": {"types": "./src/global-state.ts", "development": "./src/global-state.ts", "default": "./dist/global-state.mjs"}}, "publishConfig": {"exports": {"./constants": {"types": "./dist/constants/index.d.ts", "default": "./dist/constants/index.mjs"}, "./utils": {"types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.mjs"}, "./color": {"types": "./dist/color/index.d.ts", "default": "./dist/color/index.mjs"}, "./cache": {"types": "./dist/cache/index.d.ts", "default": "./dist/cache/index.mjs"}, "./store": {"types": "./dist/store.d.ts", "default": "./dist/store.mjs"}, "./global-state": {"types": "./dist/global-state.d.ts", "default": "./dist/global-state.mjs"}}}, "dependencies": {"@ctrl/tinycolor": "catalog:", "@tanstack/vue-store": "catalog:", "@vue/shared": "catalog:", "clsx": "catalog:", "dayjs": "catalog:", "defu": "catalog:", "lodash.clonedeep": "catalog:", "lodash.get": "catalog:", "lodash.groupby": "catalog:", "lodash.isequal": "catalog:", "lodash.set": "catalog:", "nprogress": "catalog:", "tailwind-merge": "catalog:", "theme-colors": "catalog:"}, "devDependencies": {"@types/lodash.clonedeep": "catalog:", "@types/lodash.get": "catalog:", "@types/lodash.groupby": "catalog:", "@types/lodash.isequal": "catalog:", "@types/lodash.set": "catalog:", "@types/nprogress": "catalog:"}}