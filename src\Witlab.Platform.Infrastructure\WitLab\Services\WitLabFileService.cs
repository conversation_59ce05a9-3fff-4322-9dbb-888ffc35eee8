﻿using System.IO;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Web;
using Ardalis.Result;
using Microsoft.AspNetCore.Http;
using Witlab.Platform.Core.WitLab;
using Witlab.Platform.Infrastructure.WitLab.Configurations;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;

namespace Witlab.Platform.Infrastructure.WitLab.Services;

public class WitLabFileService : IWitLabFileService
{
  private readonly ILogger<WitLabFileService> _logger;
  private readonly IHttpClientFactory _httpClientFactory;
  private readonly IOptionsSnapshot<WitLabConfiguration> _witlabConfig;
  private static readonly HashSet<string> _witlabRequiredHeaders = new()
    {
        "Content-Type",
        "SL-API-Auth",
        "SL-API-Timestamp",
        "SL-API-Signature"
    };

  public WitLabFileService(
      ILogger<WitLabFileService> logger,
      IHttpClientFactory httpClientFactory,
      IOptionsSnapshot<WitLabConfiguration> witlabConfig)
  {
    _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
    _witlabConfig = witlabConfig ?? throw new ArgumentNullException(nameof(witlabConfig));

    ValidateConfiguration();
  }

  private void ValidateConfiguration()
  {
    var config = _witlabConfig.Value;
    if (string.IsNullOrEmpty(config.WITLAB_SERVER_HOST))
      throw new InvalidOperationException("WITLAB_SERVER_HOST is not configured");
    if (string.IsNullOrEmpty(config.WITLAB_SERVER_SITE))
      throw new InvalidOperationException("WITLAB_SERVER_SITE is not configured");
    if (string.IsNullOrEmpty(config.WITLAB_ADMIN_API_ACCESS_KEY))
      throw new InvalidOperationException("WITLAB_ADMIN_API_ACCESS_KEY is not configured");
    if (string.IsNullOrEmpty(config.WITLAB_ADMIN_API_SECRET_KEY))
      throw new InvalidOperationException("WITLAB_ADMIN_API_SECRET_KEY is not configured");
  }

  private HttpClient GetSignaturedWitLabClient(string method, string path)
  {
    var httpClient = _httpClientFactory.CreateClient("WitLab");
    var url = new Uri($"{_witlabConfig.Value.WITLAB_SERVER_HOST}/{HttpUtility.UrlDecode(_witlabConfig.Value.WITLAB_SERVER_SITE)}/rest.web.api/{HttpUtility.UrlDecode(path)}");
    httpClient.BaseAddress = url;

    var timestamp = DateTime.UtcNow.ToString("o");
    var ak = _witlabConfig.Value.WITLAB_ADMIN_API_ACCESS_KEY;
    var sk = _witlabConfig.Value.WITLAB_ADMIN_API_SECRET_KEY;

    var sign = GetHmacSha256Signature(url.ToString(), method, ak, sk, timestamp);

    httpClient.DefaultRequestHeaders.TryAddWithoutValidation("SL-API-Auth", ak);
    httpClient.DefaultRequestHeaders.TryAddWithoutValidation("SL-API-Timestamp", timestamp);
    httpClient.DefaultRequestHeaders.TryAddWithoutValidation("SL-API-Signature", sign);

    return httpClient;
  }

  private static string GetHmacSha256Signature(string url, string method, string accessKey, string secretKey, string timestamp)
  {
    var signature = url + method + accessKey + timestamp;
    using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
    var signatureBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(signature));
    return Convert.ToBase64String(signatureBytes);
  }

  public async Task<Result<HttpResponseMessage>> UploadFileAsync(HttpRequest request, string? path, CancellationToken ct)
  {
    HttpRequestMessage? httpRequestMessage = null;
    var method = request.Method;

    if (string.IsNullOrEmpty(path))
    {
      path = "upload";
      method = "POST";
      var createUploadTempFileRequestResult = await CreateUploadTempFileRequestAsync(request, path, ct);
      if (!createUploadTempFileRequestResult.IsSuccess)
      {
        return Result.Error(new ErrorList(createUploadTempFileRequestResult.Errors));
      }
      httpRequestMessage = createUploadTempFileRequestResult.Value;
    }
    else
    {
      var createUploadFileRequestToRouteResult = await CreateUploadFileToRouteRequestAsync(request, path, ct);
      if (!createUploadFileRequestToRouteResult.IsSuccess)
      {
        return Result.Error(new ErrorList(createUploadFileRequestToRouteResult.Errors));
      }
      httpRequestMessage = createUploadFileRequestToRouteResult.Value;
    }

    if (httpRequestMessage == null)
    {
      return Result.Error("上传文件出现错误");
    }

    using var client = GetSignaturedWitLabClient(method, path);
    var httpResponse = await client.SendAsync(httpRequestMessage, ct);

    return httpResponse;
  }

  private async Task<Result<HttpRequestMessage>> CreateUploadTempFileRequestAsync(HttpRequest request, string path, CancellationToken ct)
  {
    var httpRequestMessage = new HttpRequestMessage
    {
      RequestUri = new Uri($"{_witlabConfig.Value.WITLAB_SERVER_HOST}/{HttpUtility.UrlDecode(_witlabConfig.Value.WITLAB_SERVER_SITE)}/rest.web.api/{HttpUtility.UrlDecode(path)}"),
      Method = HttpMethod.Post
    };

    // 复制请求头
    foreach (var header in request.Headers)
    {
      if (!httpRequestMessage.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray())
          && _witlabRequiredHeaders.Contains(header.Key))
      {
        httpRequestMessage.Content?.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
      }
    }

    var formData = await request.ReadFormAsync(ct);
    if (formData.Files.Count == 0)
    {
      return Result.Error("上传文件为空");
    }

    var file = formData.Files.First();
    // 处理请求体
    httpRequestMessage.Content = new StreamContent(file.OpenReadStream());

    if (httpRequestMessage.Content != null)
    {
      httpRequestMessage.Content.Headers.ContentType = new MediaTypeHeaderValue("application/octet-stream");
    }

    return httpRequestMessage;
  }

  private async Task<Result<HttpRequestMessage>> CreateUploadFileToRouteRequestAsync(HttpRequest request, string path, CancellationToken ct)
  {
    var httpRequestMessage = new HttpRequestMessage
    {
      RequestUri = new Uri($"{_witlabConfig.Value.WITLAB_SERVER_HOST}/{HttpUtility.UrlDecode(_witlabConfig.Value.WITLAB_SERVER_SITE)}/rest.web.api/{HttpUtility.UrlDecode(path)}"),
      Method = new HttpMethod(request.Method)
    };

    // 复制请求头
    foreach (var header in request.Headers)
    {
      if (!httpRequestMessage.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray())
          && _witlabRequiredHeaders.Contains(header.Key))
      {
        httpRequestMessage.Content?.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
      }
    }

    var formData = await request.ReadFormAsync(ct);

    // 设置formData为httpRequestMessage的content

    var multipartContent = new MultipartFormDataContent(request.ContentType.Split(';').LastOrDefault(string.Empty)
                                                              .Replace("boundary=", string.Empty)
                                                              .Replace("-", string.Empty)
                                                              .Trim());

    foreach (var formFile in formData.Files)
    {
      var streamContent = new StreamContent(formFile.OpenReadStream());
      streamContent.Headers.ContentType = new MediaTypeHeaderValue(formFile.ContentType ?? "application/octet-stream");
      multipartContent.Add(streamContent, formFile.Name, formFile.FileName);
    }
    foreach (var key in formData.Keys)
    {
      var value = formData[key];
      multipartContent.Add(new StringContent(value.ToString()), key);
    }
    httpRequestMessage.Content = multipartContent;

    if (httpRequestMessage.Content != null)
    {
      var preContentType = request.ContentType.Split(';').FirstOrDefault("multipart/form-data");
      var boundaryValue = request.ContentType.Split(';').Length > 1 ? request.ContentType.Split(';').LastOrDefault()?.Replace("boundary=", string.Empty).Replace("-", string.Empty).Trim() : null;

      httpRequestMessage.Content.Headers.ContentType = new MediaTypeHeaderValue(preContentType);
      if (boundaryValue != null)
      {
        httpRequestMessage.Content.Headers.ContentType.Parameters.Add(new NameValueHeaderValue("boundary", boundaryValue));
      }
    }

    return httpRequestMessage;
  }

  public async Task<Result<HttpResponseMessage>> DownloadFileAsync(HttpRequest request, string path, CancellationToken ct)
  {
    var httpRequestMessage = new HttpRequestMessage
    {
      RequestUri = new Uri($"{_witlabConfig.Value.WITLAB_SERVER_HOST}/{HttpUtility.UrlDecode(_witlabConfig.Value.WITLAB_SERVER_SITE)}/rest.web.api/{HttpUtility.UrlDecode(path)}"),
      Method = new HttpMethod(request.Method)
    };

    // 复制请求头
    foreach (var header in request.Headers)
    {
      if (!httpRequestMessage.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray())
          && _witlabRequiredHeaders.Contains(header.Key))
      {
        httpRequestMessage.Content?.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
      }
    }

    // 复制 httpRequest.Body 到 request.Body
    var memoryStream = new MemoryStream();

    if (request.Body.CanSeek)
    {
      request.Body.Position = 0;
    }

    await request.Body.CopyToAsync(memoryStream, ct);
    memoryStream.Position = 0;

    httpRequestMessage.Content = new StreamContent(memoryStream);
    

    if (httpRequestMessage.Content != null)
    {
      var preContentType = request.ContentType?.Split(';').FirstOrDefault() ?? "application/json";
      var preCharSet = request.ContentType?.Split(';').Length > 1 ? request.ContentType.Split(';').LastOrDefault("UTF-8").Replace("charset=", string.Empty) : "UTF-8";

      httpRequestMessage.Content.Headers.ContentType = new MediaTypeHeaderValue(preContentType);
    }
    using var client = GetSignaturedWitLabClient(httpRequestMessage.Method.ToString(), path);
    var httpResponse = await client.SendAsync(httpRequestMessage, ct);

    return httpResponse;
  }
}
