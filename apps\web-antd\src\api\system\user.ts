import { backendRequestClient, requestClient } from '#/api/request';

import { callServer } from '../core';

export namespace SystemUserApi {
  /**
   * 用户数据传输对象
   */
  export interface User {
    userId: string; // C# Guid 对应 string

    userName: string;

    realName: string;

    password?: string;

    email?: string;

    phone?: number; // C# long 对应 number

    address?: string;

    icon?: string;

    sexValue: number;

    sexName: string;

    stateValue: number;

    stateName: string;

    remark?: string;

    depts?: string[];

    roles?: string[];

    createdOnUtc: string; // C# DateTime 对应 string (ISO 8601)

    createdBy?: string;

    lastModifiedOnUtc?: string; // C# DateTime? 对应 string?

    lastModifiedBy?: string;
  }
}

/**
 * 获取用户列表数据
 */
async function getUserList(params: SystemUserApi.User) {
  const items = await backendRequestClient.get<Array<SystemUserApi.User>>(
    '/platform/users',

    {
      params,
    },
  );

  return {
    items: items ?? [],

    total: items?.length ?? 0,
  };
}

/**
 * 创建用户
 * @param data 用户数据
 */

async function createUser(
  data: Omit<
    SystemUserApi.User,
    | 'createdBy'
    | 'createdOnUtc'
    | 'lastModifiedBy'
    | 'lastModifiedOnUtc'
    | 'sexName'
    | 'stateName'
    | 'userId'
  >,
) {
  return backendRequestClient.post('/platform/users', data);
}

/**
 * 更新用户
 *
 * @param id 用户 ID
 * @param data 用户数据
 */
/**
 * 更新用户
 * @param id - 要更新的用户ID
 * @param data - 用户更新数据，排除了以下字段:
 *  - createdBy: 创建人
 *  - createdOnUtc: 创建时间
 *  - id: 用户ID
 *  - lastModifiedBy: 最后修改人
 *  - lastModifiedOnUtc: 最后修改时间
 *  - sexName: 性别名称
 *  - stateName: 状态名称
 */

async function updateUser(
  id: string,

  data: Omit<
    SystemUserApi.User,
    | 'createdBy'
    | 'createdOnUtc'
    | 'lastModifiedBy'
    | 'lastModifiedOnUtc'
    | 'password'
    | 'userId'
  >,
) {
  return backendRequestClient.put(`/platform/users/${id}`, data);
}

async function updateUserState(id: string, state: number) {
  return backendRequestClient.post(
    `/platform/users/${id}/${state === 1 ? 'activate' : 'deactivate'}`,

    {},
  );
}

/**
 * 删除用户
 * @param id 用户 ID
 */
async function deleteUser(id: string) {
  return backendRequestClient.delete(`/platform/users/${id}`);
}

/**
 * 获取单个用户数据
 * @param id 用户 ID
 */
async function getUser(id: string) {
  return backendRequestClient.get<SystemUserApi.User>(`/platform/users/${id}`);
}

interface UploadFileParams {
  file: File;

  onError?: (error: Error) => void;

  onProgress?: (progress: { percent: number }) => void;

  onSuccess?: (data: any, file: File) => void;
}

export async function upload_file({
  file,

  onError,

  onProgress,

  onSuccess,
}: UploadFileParams) {
  try {
    onProgress?.({ percent: 0 });

    const data = await requestClient.upload('/upload', { file });

    onProgress?.({ percent: 100 });

    onSuccess?.(data, file);
  } catch (error) {
    onError?.(error instanceof Error ? error : new Error(String(error)));
  }
}

async function getRolesForSelect() {
  return await backendRequestClient.get('/platform/roles');
}

async function getDeptsForSelect() {
  return await backendRequestClient.get('/platform/depts');
}

async function getUserInfoFromWitlab(userName: string): Promise<{
  answer: string;

  deptList: string;

  disableLogin: string;

  email: string;

  engStarDocId: string;

  fullName: string;

  jobDescription: string;

  origRec: string;

  question: string;

  starDocId: string;

  status: string;

  userName: string;

  userPhone: string;
}> {
  const data = await callServer('UserManagement.getSingleUserInfo', [userName]);

  const [
    status,

    _userName,

    fullName,

    deptList,

    origRec,

    email,

    jobDescription,

    userPhone,

    question,

    answer,

    starDocId,

    disableLogin,

    engStarDocId,
  ] = data.length > 0 ? data[0] : [];

  return {
    answer,

    deptList,

    disableLogin,

    email,

    engStarDocId,

    fullName,

    jobDescription,

    origRec,

    question,

    starDocId,

    status,

    userName: _userName,

    userPhone,
  };
}

export {
  createUser,
  deleteUser,
  getDeptsForSelect,
  getRolesForSelect,
  getUser,
  getUserInfoFromWitlab,
  getUserList,
  updateUser,
  updateUserState,
};
