﻿using System.Text.Json;
using System.Text.Json.Serialization;
using Ardalis.Result;
using MediatR;
using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;
using Witlab.Platform.UseCases.WitLab.CreateOrUpdateLabAccessKeyParis;

namespace Witlab.Platform.Infrastructure.Platform.Handlers;

/// <summary>
/// 用户删除事件处理器
/// </summary>
public class UserDeptAssignedHandler : INotificationHandler<UserDeptAssignedEvent>
{
  private readonly ILogger<UserDeptAssignedHandler> _logger;
  private readonly IMediator _mediator;
  private readonly IWitLabSyncService _witLabSyncService;

  public UserDeptAssignedHandler(ILogger<UserDeptAssignedHandler> logger,IMediator mediator, IWitLabSyncService witLabSyncService)
  {
    _logger = logger;
    _mediator = mediator;
    _witLabSyncService = witLabSyncService;
  }

  public async Task Handle(UserDeptAssignedEvent notification, CancellationToken cancellationToken)
  {
    var syncResult = await _witLabSyncService.SyncUserDeptsAsync(notification.UserName, notification.DeptCodes, cancellationToken);
    if (syncResult.IsError())
      throw new Exception(string.Join(Environment.NewLine, syncResult.Errors));

    foreach (var deptCode in notification.DeptCodes)
    {
      var getAccessKeysResult = await _witLabSyncService.GetUserAccessKeysAsync(notification.UserName, deptCode, cancellationToken);
      if (getAccessKeysResult.IsError())
        throw new Exception(string.Join(Environment.NewLine, getAccessKeysResult.Errors));

      var accessKeysResult = getAccessKeysResult.Value.Result?.ToString();

      Guard.Against.Null(accessKeysResult, nameof(accessKeysResult), "Access keys cannot be null.");

      var accessKeys = JsonSerializer.Deserialize<List<string?>>(accessKeysResult);

      Guard.Against.Null(accessKeys, nameof(accessKeys), "Access keys cannot be null.");

      var accessKey = accessKeys?.FirstOrDefault();
      var seceretKey = accessKeys?.LastOrDefault();

      if (string.IsNullOrEmpty(accessKey) || string.IsNullOrEmpty(seceretKey))
      {
        _logger.LogWarning("Access keys for user {UserName} in department {DeptCode} are not set.", notification.UserName, deptCode);
        continue;
      }

      await _mediator.Send(new CreateOrUpdateLabAccessKeysCommand(notification.UserName, deptCode, accessKey, seceretKey), cancellationToken);
    }

  }
}
