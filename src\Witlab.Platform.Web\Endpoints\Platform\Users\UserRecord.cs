﻿using System.Text.Json.Serialization;

namespace Witlab.Platform.Web.Endpoints.Platform.Users;

/// <summary>
/// 用户记录
/// </summary>
public record UserRecord(
    [property: JsonPropertyName("userId")] Guid Id,
    string UserName,
    [property: JsonPropertyName("realName")] string FullName,
    string? Email,
    long? Phone,
    string? Address,
    string? Icon,
    int SexValue,
    string SexName,
    int StateValue,
    string StateName,
    string? Remark,
    List<string> Roles,
    List<string> Depts,
    DateTime CreatedOnUtc,
    string? CreatedBy,
    DateTime? LastModifiedOnUtc,
    string? LastModifiedBy
);
