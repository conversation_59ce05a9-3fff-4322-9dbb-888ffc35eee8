﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.WitLab;
using Witlab.Platform.Infrastructure.Data.Config.Base;

namespace Witlab.Platform.Infrastructure.Data.Config.WitLab;

public class LabAccessKeyParisConfiguration : IEntityTypeConfiguration<LabAccessKeys>
{
  public void Configure(EntityTypeBuilder<LabAccessKeys> builder)
  {
    builder.HasKey(x => x.Id);

    builder.Property(p => p.UserName)
        .HasMaxLength(50)
        .IsRequired();

    builder.Property(p => p.DeptCode)
        .HasMaxLength(50)
        .IsRequired();

    builder.Property(p => p.RoleCode)
        .HasMaxLength(50);

    // 配置UserState SmartEnum转换
    builder.Property(p => p.State)
        .HasConversion(
            v => v.Value,
            v => LabAccessKeysState.FromValue(v));

    builder.Property(p => p.AccessKey)
        .IsRequired()
        .HasMaxLength(100);

    builder.Property(p => p.<PERSON>ey)
      .IsRequired()
      .HasMaxLength(100);

    builder.ToTable("LabAccessKeys");
  }
}
