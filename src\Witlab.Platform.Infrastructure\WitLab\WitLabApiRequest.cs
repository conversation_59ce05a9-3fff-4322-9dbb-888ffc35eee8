﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace Witlab.Platform.Infrastructure.WitLab;
public class WitLabApiRequest
{
  public string Method { get; set; } = "GET";

  public Dictionary<string, List<string?>> Headers { get; set; } = new Dictionary<string, List<string?>>();

  public string ContentType { get; set; } = "application/json";

  public Stream Body { get; set; } = new MemoryStream();

}
