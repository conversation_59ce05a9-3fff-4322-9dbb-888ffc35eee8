﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Witlab.Platform.Core.Platform;

public class WitLabAccessKeyParisState : SmartEnum<WitLabAccessKeyParisState>
{
  public static readonly WitLabAccessKeyParisState Deactivate = new(nameof(Deactivate), 0);
  public static readonly WitLabAccessKeyParisState Activate = new(nameof(Activate), 1);

  protected WitLabAccessKeyParisState(string name, int value) : base(name, value) { }
}
