﻿using MediatR;
using Microsoft.Extensions.Logging;
using Witlab.Platform.Core.Common.Interfaces;
using Witlab.Platform.Core.Platform.Events;

namespace Witlab.Platform.Infrastructure.Platform.Handlers;

/// <summary>
/// 用户删除事件处理器
/// </summary>
public class UserDeletedHandler : INotificationHandler<UserDeletedEvent>
{
    private readonly ILogger<UserDeletedHandler> _logger;
    private readonly IEmailSender _emailSender;

    public UserDeletedHandler(ILogger<UserDeletedHandler> logger, IEmailSender emailSender)
    {
        _logger = logger;
        _emailSender = emailSender;
    }

    public async Task Handle(UserDeletedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("处理用户删除事件: 用户ID {UserId}, 用户名 {UserName}", notification.UserId, notification.UserName);

        // 这里可以添加用户删除后的业务逻辑，例如发送通知邮件
        await _emailSender.SendEmailAsync(
            "<EMAIL>",
            "<EMAIL>",
            "用户删除通知",
            $"用户已删除，用户ID: {notification.UserId}, 用户名: {notification.UserName}"
        );
    }
}
