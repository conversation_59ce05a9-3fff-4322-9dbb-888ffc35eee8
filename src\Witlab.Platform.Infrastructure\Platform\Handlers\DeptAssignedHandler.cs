﻿using MediatR;
using Microsoft.Extensions.Logging;
using Witlab.Platform.Core.Platform.Events;

namespace Witlab.Platform.Infrastructure.Platform.Handlers;

/// <summary>
/// 部门分配事件处理器
/// </summary>
public class DeptAssignedHandler : INotificationHandler<DeptAssignedEvent>
{
    private readonly ILogger<DeptAssignedHandler> _logger;

    public DeptAssignedHandler(ILogger<DeptAssignedHandler> logger)
    {
        _logger = logger;
    }

    public Task Handle(DeptAssignedEvent notification, CancellationToken cancellationToken)
    {
        _logger.LogInformation("处理部门分配事件: 用户ID {UserId}, 部门ID {DeptId}", 
            notification.UserId, notification.DeptId);

        // 这里可以添加部门分配后的业务逻辑
        return Task.CompletedTask;
    }
}
