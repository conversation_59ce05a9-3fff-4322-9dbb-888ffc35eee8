﻿<Project Sdk="Microsoft.NET.Sdk">
  <Sdk Name="Microsoft.Build.CentralPackageVersions" Version="2.1.3" />
  <ItemGroup>
    <Compile Remove="Data\Config\Platform\RoleMenuConfiguration.cs" />
    <Compile Remove="Data\Config\RefreshTokenConfiguration.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.Specification.EntityFrameworkCore" />
    <PackageReference Include="Azure.Identity" />
    <PackageReference Include="MailKit" />
    <PackageReference Include="MediatR" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" PrivateAssets="all" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Http" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" />
    <PackageReference Include="SQLite" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\Witlab.Platform.Core\Witlab.Platform.Core.csproj" />
    <ProjectReference Include="..\WitLab.Platform.SharedKernel\WitLab.Platform.SharedKernel.csproj" />
    <ProjectReference Include="..\Witlab.Platform.UseCases\Witlab.Platform.UseCases.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <Folder Include="Data\Config\Platform\" />
  </ItemGroup>
</Project>
