﻿using Ardalis.Result;
using Witlab.Platform.Infrastructure.Auth.Models;

namespace Witlab.Platform.Infrastructure.Auth.Interfaces;

/// <summary>
/// 认证服务接口
/// </summary>
public interface IAuthService
{
    /// <summary>
    /// 登录
    /// </summary>
    /// <param name="userName">用户名</param>
    /// <param name="password">密码</param>
    /// <returns>登录结果</returns>
    Task<Result<AuthResponse>> LoginAsync(string userName, string password);

    /// <summary>
    /// 注销
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="accessToken">访问令牌</param>
    /// <param name="refreshToken">刷新令牌</param>
    /// <returns>注销结果</returns>
    Task<Result> LogoutAsync(Guid userId, string accessToken, string refreshToken);

    /// <summary>
    /// 刷新令牌
    /// </summary>
    /// <param name="accessToken">访问令牌</param>
    /// <param name="refreshToken">刷新令牌</param>
    /// <returns>刷新结果</returns>
    Task<Result<AuthResponse>> RefreshTokenAsync(string accessToken, string refreshToken);

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户信息</returns>
    Task<Result<UserInfo>> GetUserInfoAsync(Guid userId);
}
