﻿using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.Infrastructure.Auth.Interfaces;

/// <summary>
/// JWT服务接口
/// </summary>
public interface IJwtService
{
    /// <summary>
    /// 生成访问令牌
    /// </summary>
    /// <param name="user">用户</param>
    /// <param name="roles">角色列表</param>
    /// <returns>访问令牌</returns>
    string GenerateAccessToken(User user, IEnumerable<Role> roles);

    /// <summary>
    /// 生成刷新令牌
    /// </summary>
    /// <returns>刷新令牌</returns>
    string GenerateRefreshToken();

    /// <summary>
    /// 验证访问令牌
    /// </summary>
    /// <param name="token">访问令牌</param>
    /// <returns>验证结果</returns>
    bool ValidateAccessToken(string token);

    /// <summary>
    /// 从访问令牌中获取用户ID
    /// </summary>
    /// <param name="token">访问令牌</param>
    /// <returns>用户ID</returns>
    Guid? GetUserIdFromToken(string token);

    /// <summary>
    /// 从访问令牌中获取用户名
    /// </summary>
    /// <param name="token">访问令牌</param>
    /// <returns>用户名</returns>
    string? GetUserNameFromToken(string token);

    /// <summary>
    /// 从访问令牌中获取角色列表
    /// </summary>
    /// <param name="token">访问令牌</param>
    /// <returns>角色列表</returns>
    IEnumerable<string> GetRolesFromToken(string token);

    /// <summary>
    /// 将令牌加入黑名单
    /// </summary>
    /// <param name="token">令牌</param>
    /// <returns>操作结果</returns>
    Task<bool> AddToBlacklistAsync(string token);

    /// <summary>
    /// 检查令牌是否在黑名单中
    /// </summary>
    /// <param name="token">令牌</param>
    /// <returns>检查结果</returns>
    Task<bool> IsInBlacklistAsync(string token);
}
