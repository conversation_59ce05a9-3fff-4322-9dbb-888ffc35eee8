﻿using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using Witlab.Platform.Infrastructure.WitLab.Configurations;
using Witlab.Platform.Infrastructure.WitLab.Extensions;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;

namespace Witlab.Platform.Infrastructure.WitLab.Services;

public class WitLabProxyService : IWitLabProxyService
{
  private readonly ILogger<WitLabProxyService> _logger;
  private readonly IHttpClientFactory _httpClientFactory;
  private readonly IOptionsSnapshot<WitLabConfiguration> _witlabConfig;
  private static readonly HashSet<string> _witlabRequiredHeaders = new()
    {
        "Content-Type",
        "SL-API-Auth",
        "SL-API-Timestamp",
        "SL-API-Signature"
    };

  public WitLabProxyService(
      ILogger<WitLabProxyService> logger,
      IHttpClientFactory httpClientFactory,
      IOptionsSnapshot<WitLabConfiguration> witlabConfig)
  {
    _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
    _witlabConfig = witlabConfig ?? throw new ArgumentNullException(nameof(witlabConfig));

    ValidateConfiguration();
  }

  private void ValidateConfiguration()
  {
    var config = _witlabConfig.Value;
    if (string.IsNullOrEmpty(config.WITLAB_SERVER_HOST))
      throw new InvalidOperationException("WITLAB_SERVER_HOST is not configured");
    if (string.IsNullOrEmpty(config.WITLAB_SERVER_SITE))
      throw new InvalidOperationException("WITLAB_SERVER_SITE is not configured");
    if (string.IsNullOrEmpty(config.WITLAB_ADMIN_API_ACCESS_KEY))
      throw new InvalidOperationException("WITLAB_ADMIN_API_ACCESS_KEY is not configured");
    if (string.IsNullOrEmpty(config.WITLAB_ADMIN_API_SECRET_KEY))
      throw new InvalidOperationException("WITLAB_ADMIN_API_SECRET_KEY is not configured");
  }

  public async Task<WitLabApiResponse> ForwardAsync(WitLabApiRequest request, string path, CancellationToken ct)
  {
    try
    {
      _logger.LogInformation("Forwarding request to WitLab Server: {Method} {Path}", request.Method, path);

      using var client = GetSignaturedWitLabClient(request.Method, path);
      var httpRequest = CreateHttpRequestMessage(request, path);

      var httpResponse = await client.SendAsync(httpRequest, ct);
      //var response = await new WitLabApiResponse().FromHttpResponse(httpResponse);
      var response = await httpResponse.ToWitLabApiResponse(ct);

      _logger.LogInformation("Received response from WitLab Server: {StatusCode}", response.StatusCode);

      return response;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error forwarding request to WitLab Server: {Method} {Path}", request.Method, path);
      throw;
    }
  }

  private HttpRequestMessage CreateHttpRequestMessage(WitLabApiRequest request, string path)
  {
    var httpRequestMessage = new HttpRequestMessage
    {
      RequestUri = new Uri($"{_witlabConfig.Value.WITLAB_SERVER_HOST}/{HttpUtility.UrlDecode(_witlabConfig.Value.WITLAB_SERVER_SITE)}/rest.web.api/{HttpUtility.UrlDecode(path)}"),
      Method = new HttpMethod(request.Method)
    };

    // 复制请求头
    foreach (var header in request.Headers)
    {
      if (!httpRequestMessage.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray())
          && _witlabRequiredHeaders.Contains(header.Key))
      {
        httpRequestMessage.Content?.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
      }
    }

    // 处理请求体
    httpRequestMessage.Content = new StreamContent(request.Body);

    if (httpRequestMessage.Content is not null)
    {
      var preContentType = request.ContentType?.Split(';').FirstOrDefault() ?? "application/json";
      var preCharSet = request.ContentType?.Split(';').Length > 1 ? request.ContentType.Split(';').LastOrDefault("UTF-8").Replace("charset=", string.Empty) : "UTF-8";

      httpRequestMessage.Content.Headers.ContentType = new MediaTypeHeaderValue(preContentType);
    }

    return httpRequestMessage;
  }

  private HttpClient GetSignaturedWitLabClient(string method, string path)
  {
    var httpClient = _httpClientFactory.CreateClient("WitLab");
    var url = new Uri($"{_witlabConfig.Value.WITLAB_SERVER_HOST}/{HttpUtility.UrlDecode(_witlabConfig.Value.WITLAB_SERVER_SITE)}/rest.web.api/{HttpUtility.UrlDecode(path)}");
    httpClient.BaseAddress = url;

    var timestamp = DateTime.UtcNow.ToString("o");
    var ak = _witlabConfig.Value.WITLAB_ADMIN_API_ACCESS_KEY;
    var sk = _witlabConfig.Value.WITLAB_ADMIN_API_SECRET_KEY;

    var sign = GetHmacSha256Signature(url.ToString(), method, ak, sk, timestamp);

    httpClient.DefaultRequestHeaders.TryAddWithoutValidation("SL-API-Auth", ak);
    httpClient.DefaultRequestHeaders.TryAddWithoutValidation("SL-API-Timestamp", timestamp);
    httpClient.DefaultRequestHeaders.TryAddWithoutValidation("SL-API-Signature", sign);

    return httpClient;
  }

  private static string GetHmacSha256Signature(string url, string method, string accessKey, string secretKey, string timestamp)
  {
    var signature = url + method + accessKey + timestamp;
    using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
    var signatureBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(signature));
    return Convert.ToBase64String(signatureBytes);
  }

}
