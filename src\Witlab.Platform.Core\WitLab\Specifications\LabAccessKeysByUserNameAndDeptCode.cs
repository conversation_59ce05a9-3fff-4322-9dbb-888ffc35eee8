﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Witlab.Platform.Core.WitLab.Specifications;
public class LabAccessKeysByUserNameAndDeptCode :Specification<LabAccessKeys>
{
    public LabAccessKeysByUserNameAndDeptCode(string userName, string deptCode)
    {
        if (string.IsNullOrWhiteSpace(userName)) throw new ArgumentException("User name cannot be null or empty.", nameof(userName));
        if (string.IsNullOrWhiteSpace(deptCode)) throw new ArgumentException("Department code cannot be null or empty.", nameof(deptCode));
        Query.Where(x => x.UserName == userName && x.DeptCode == deptCode);
    }
}
