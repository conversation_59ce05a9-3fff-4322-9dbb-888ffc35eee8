﻿﻿using Witlab.Platform.UseCases.Platform.Depts.GetTree;
using Witlab.Platform.Web.Configurations.Auth;

namespace Witlab.Platform.Web.Endpoints.Platform.Depts;

/// <summary>
/// 获取部门树
/// </summary>
[RequirePermission("dept:view")]
public class GetTree(IMediator _mediator) : Endpoint<GetDeptTreeRequest, DeptListResponse>
{
    public override void Configure()
    {
        Get(GetDeptTreeRequest.Route);
        Description(x => x.AutoTagOverride("Dept"));
        Summary(s =>
        {
            s.Summary = "获取部门树";
            s.Description = "获取部门的树形结构";
        });
    }

    public override async Task HandleAsync(GetDeptTreeRequest request, CancellationToken cancellationToken)
    {
        var query = new GetDeptTreeQuery(request.ParentId);

        var result = await _mediator.Send(query, cancellationToken);

        if (result.IsSuccess)
        {
            var depts = result.Value.Select(dto => new DeptRecord()
            {
              DeptId = dto.Id,
              DeptName = dto.DeptName,
              DeptCode = dto.DeptCode,
              ParentId = dto.ParentId,
              Leader = dto.Leader,
              OrderNum = dto.OrderNum,
              Status = dto.StateValue,
              Remark = dto.Remark,
              CreatedOnUtc = dto.CreatedOnUtc,
              CreatedBy = dto.CreatedBy,
              LastModifiedOnUtc = dto.LastModifiedOnUtc,
              LastModifiedBy = dto.LastModifiedBy
            }).ToList();

            Response = new DeptListResponse
            {
                Depts = depts
            };
            return;
        }

        // 处理错误
        if (result.Status == ResultStatus.NotFound)
        {
            await SendNotFoundAsync(cancellationToken);
            return;
        }

        await SendErrorsAsync(cancellation: cancellationToken);
    }
}
