﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Witlab.Platform.Core.WitLab;

public class LabAccessKeys(string userName, string deptCode, string? roleCode, string accessKey, string secretKey) : EntityBase<Guid>, IAggregateRoot
{
  public string UserName { get; private set; } = Guard.Against.NullOrEmpty(userName, nameof(userName));
  public string DeptCode { get; private set; } = Guard.Against.NullOrEmpty(deptCode, nameof(deptCode));
  public string? RoleCode { get; private set; } = roleCode;
  public string AccessKey { get; private set; } = Guard.Against.NullOrEmpty(accessKey, nameof(accessKey));
  public string SecretKey { get; private set; } = Guard.Against.NullOrEmpty(secretKey, nameof(secretKey));

  public LabAccessKeysState State { get; private set; } = LabAccessKeysState.Activate;

  public void Activate() => State = LabAccessKeysState.Activate;
  public void Deactivate() => State = LabAccessKeysState.Deactivate;
}
