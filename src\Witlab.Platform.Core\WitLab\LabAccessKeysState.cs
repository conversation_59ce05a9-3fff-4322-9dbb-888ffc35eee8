﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Witlab.Platform.Core.WitLab;

public class LabAccessKeysState : SmartEnum<LabAccessKeysState>
{
  public static readonly LabAccessKeysState Deactivate = new(nameof(Deactivate), 0);
  public static readonly LabAccessKeysState Activate = new(nameof(Activate), 1);

  protected LabAccessKeysState(string name, int value) : base(name, value) { }
}
