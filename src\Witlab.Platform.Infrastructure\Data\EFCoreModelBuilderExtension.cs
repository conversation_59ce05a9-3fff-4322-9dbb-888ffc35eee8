﻿namespace Witlab.Platform.Infrastructure.Data;
internal static class EFCoreModelBuilderExtension
{
    internal static void SetTableNamePrefix(this ModelBuilder builder, string tablePrefix)
    {
        // 设置表名前缀
        var entities = builder.Model.GetEntityTypes();
        foreach (var entity in entities)
        {
            var tableName = entity.GetTableName();
            if (!string.IsNullOrEmpty(tableName) && !entity.IsOwned())
            {
                entity.SetTableName($"{tablePrefix}_{tableName}");
            }
        }
    }
}
