﻿using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Data;
using Witlab.Platform.UseCases.Platform;
using Witlab.Platform.UseCases.Platform.Menus.List;

namespace Witlab.Platform.Infrastructure.Platform.Queries;

/// <summary>
/// 列出菜单查询服务实现
/// </summary>
public class ListMenusQueryService : IListMenusQueryService
{
  private readonly AppDbContext _dbContext;

  public ListMenusQueryService(AppDbContext dbContext)
  {
    _dbContext = dbContext;
  }

  public async Task<IEnumerable<MenuDTO>> ListAsync(int? skip = null, int? take = null, MenuType? menuType = null)
  {
    IQueryable<Menu> query = _dbContext.Menus;

    if (menuType != null)
    {
      query = query.Where(m => m.MenuType == menuType);
    }

    if (skip.HasValue)
    {
      query = query.Skip(skip.Value);
    }

    if (take.HasValue)
    {
      query = query.Take(take.Value);
    }

    var menus = await query.ToListAsync();

    return menus.Select(menu => new MenuDTO(
        menu.Id,
        menu.MenuName,
        menu.ParentId,
        menu.MenuType.Value,
        menu.MenuType.Name,
        menu.OrderNum,
        menu.State.Value,
        menu.State.Name,
        menu.MenuIcon,
        menu.ActiveIcon,
        menu.Router,
        menu.RouterName,
        menu.Redirect,
        menu.Component,
        menu.Query,
        menu.Remark,
        menu.Title,
        menu.AffixTab,
        menu.AffixTabOrder,
        menu.Badge,
        menu.BadgeType,
        menu.BadgeVariants,
        menu.IframeSrc,
        menu.Link,
        menu.OpenInNewWindow,
        menu.KeepAlive,
        menu.HideInMenu,
        menu.HideInTab,
        menu.HideInBreadcrumb,
        menu.HideChildrenInMenu,
        menu.ActivePath,
        menu.MaxNumOfOpenTab,
        menu.NoBasicLayout,
        menu.Created.DateTime,
        menu.CreatedBy,
        menu.LastModified.DateTime,
        menu.LastModifiedBy
    ));
  }
}
