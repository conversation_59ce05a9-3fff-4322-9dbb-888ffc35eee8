﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Core.Platform.Services;
using Witlab.Platform.Core.WitLab.Interfaces;
using Witlab.Platform.Core.WitLab.Specifications;

namespace Witlab.Platform.Core.WitLab.Services;
public class LabAccessKeysService : ILabAccessKeysService
{
  private readonly ILogger<LabAccessKeysService> _logger;
  private readonly IRepository<LabAccessKeys> _repository;

  public LabAccessKeysService(ILogger<LabAccessKeysService> logger, IRepository<LabAccessKeys> repository)
  {
    _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    _repository = repository ?? throw new ArgumentNullException(nameof(repository));
  }

  public async Task<Result<LabAccessKeys>> CreateKeysAsync(string userName, string deptCode, string accessKey, string secretKey, CancellationToken cancellationToken = default)
  {
    if (string.IsNullOrEmpty(userName)) throw new ArgumentNullException(nameof(userName));
    if (string.IsNullOrEmpty(deptCode)) throw new ArgumentNullException(nameof(deptCode));
    if (string.IsNullOrEmpty(accessKey)) throw new ArgumentNullException(nameof(accessKey));
    if (string.IsNullOrEmpty(secretKey)) throw new ArgumentNullException(nameof(secretKey));

    var labAccessKeys = new LabAccessKeys(userName, deptCode, null, accessKey, secretKey);
    await _repository.AddAsync(labAccessKeys, cancellationToken);
    return Result.Success(labAccessKeys);
  }

  public async Task<Result<LabAccessKeys>> GetKeysByUserNameAndDeptCode(string userName, string deptCode, CancellationToken cancellationToken = default)
  {
    var spec = new LabAccessKeysByUserNameAndDeptCode(userName, deptCode);
    var keys = await _repository.FirstOrDefaultAsync(spec, cancellationToken);

    if (keys == null)
    {
      return Result.NotFound("密钥不存在");
    }

    return Result.Success(keys);
  }

  public async Task<Result> DeleteKeysAsync(Guid guid, CancellationToken cancellationToken = default)
  {
    try
    {
      var keys = await _repository.GetByIdAsync(guid, cancellationToken);
      if (keys == null)
      {
        return Result.NotFound("密钥不存在");
      }

      await _repository.DeleteAsync(keys, cancellationToken);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "删除密钥时发生错误: {Message}", ex.Message);
      return Result.Error($"删除密钥失败: {ex.Message}");
    }
  }
}
