﻿using Ardalis.Result;
using MediatR;
using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;

namespace Witlab.Platform.Infrastructure.Platform.Handlers;

/// <summary>
/// 用户创建事件处理器
/// </summary>
public class UserCreatedHandler : INotificationHandler<UserCreatedEvent>
{
  private readonly ILogger<UserCreatedHandler> _logger;
  private readonly IWitLabSyncService _witLabSyncService;

  public UserCreatedHandler(ILogger<UserCreatedHandler> logger, IWitLabSyncService witLabSyncService)
  {
    _logger = logger;
    _witLabSyncService = witLabSyncService;
  }

  public async Task Handle(UserCreatedEvent notification, CancellationToken cancellationToken)
  {
    _logger.LogInformation("处理用户创建事件: 用户ID {UserId}, 用户名 {UserName}", notification.UserId, notification.UserName);

    var syncAddResult = await _witLabSyncService.SyncAddUserInfoAsync(notification.UserName, notification.FullName, notification.Email, cancellationToken);
    if (syncAddResult.IsError()) 
      throw new Exception(string.Join(Environment.NewLine,syncAddResult.Errors));

    var setPasswordResult = await _witLabSyncService.SyncChangeUserPasswordAsync(notification.UserName, notification.Password, cancellationToken);
    if (setPasswordResult.IsError())
      throw new Exception(string.Join(Environment.NewLine, setPasswordResult.Errors));

    _logger.LogInformation("同步至WitLab Server: 用户名：{ UserName }", notification.UserName);
  }
}
