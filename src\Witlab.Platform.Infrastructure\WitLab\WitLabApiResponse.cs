﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace Witlab.Platform.Infrastructure.WitLab;
public class WitLabApiResponse
{
  public List<KeyValuePair<string, IEnumerable<string>>> Headers { get; set; } = new List<KeyValuePair<string, IEnumerable<string>>>();
  public string? Message { get; set; }
  public bool Success { get; set; }
  public int StatusCode { get; set; }
  public object? Result { get; set; }
}
