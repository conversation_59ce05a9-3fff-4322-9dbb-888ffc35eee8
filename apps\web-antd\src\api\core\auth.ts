import { useAccessStore } from '@vben/stores';

import { backendRequestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    tokenType: string;
    userId: string;
    userName: string;
  }

  export interface RefreshTokenResult {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    tokenType: string;
    userId: string;
    userName: string;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  // return requestClient.post<AuthApi.LoginResult>('/auth/login', data);
  return backendRequestClient.post<AuthApi.LoginResult>('/auth/login', data);
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  const accessStore = useAccessStore();
  return backendRequestClient.post<AuthApi.RefreshTokenResult>(
    '/auth/refresh-token',
    {
      accessToken: accessStore.accessToken,
      refreshToken: accessStore.refreshToken,
    },
  );
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return backendRequestClient.post('/auth/logout', {
    withCredentials: true,
  });
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi(userId: string) {
  const userPermissions = await backendRequestClient.get(
    `/platform/users/${userId}/permissions`,
  );
  return userPermissions
    .filter((permission: { code: string; isEnabled: boolean }) => {
      return permission.isEnabled;
    })
    .map((permission: { code: string }) => permission.code);
}

export async function validateUserPassword(userName: string, password: string) {
  return backendRequestClient.post('/platform/users/validate', {
    userName,
    password,
  });
}
